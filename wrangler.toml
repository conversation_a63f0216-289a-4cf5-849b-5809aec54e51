# Cloudflare Workers configuration for medical equipment management system
name = "quan-ly-tbyt"
compatibility_date = "2024-12-30"
compatibility_flags = ["nodejs_compat"]

# Pages configuration for Next.js deployment
pages_build_output_dir = ".vercel/output/static"

# Environment variables for Cloudflare Workers
# These will be set via Cloudflare dashboard or wrangler secrets
[env.production]
name = "quan-ly-tbyt-production"

[env.preview]
name = "quan-ly-tbyt-preview"

# Build configuration
[build]
command = "npm run build"

# Node.js compatibility for Supabase client
node_compat = true

# Custom domains (to be configured later)
# routes = [
#   { pattern = "your-domain.com/*", zone_name = "your-domain.com" }
# ]
